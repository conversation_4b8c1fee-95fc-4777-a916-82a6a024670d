import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Plus, TrendingUp, TrendingDown, Package, FileText } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import axios from 'axios'

interface StockTransaction {
  id: string
  product: {
    id: string
    productCode: string
    name: string
    image?: {
      filename: string
    }
  }
  type: 'IN' | 'OUT' | 'ADJUSTMENT'
  quantity: number
  previousStock: number
  newStock: number
  unitPrice?: number
  totalValue?: number
  reference: string
  notes?: string
  documents?: Array<{
    filename: string
    originalName: string
  }>
  createdAt: string
  createdBy: {
    name: string
  }
}

interface Product {
  id: string
  productCode: string
  name: string
  currentStock: number
}

const Stock = () => {
  const [transactions, setTransactions] = useState<StockTransaction[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState('')
  const [quantity, setQuantity] = useState('')
  const [unitPrice, setUnitPrice] = useState('')
  const [notes, setNotes] = useState('')
  const { toast } = useToast()

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [transactionsRes, productsRes] = await Promise.all([
        axios.get('/api/stock/transactions'),
        axios.get('/api/products')
      ])
      setTransactions(transactionsRes.data)
      setProducts(productsRes.data)
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to fetch data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleAddStock = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedProduct || !quantity) {
      toast({
        title: "Error",
        description: "Please select a product and enter quantity",
        variant: "destructive",
      })
      return
    }

    try {
      const formData = new FormData()
      formData.append('productId', selectedProduct)
      formData.append('quantity', quantity)
      formData.append('unitPrice', unitPrice || '0')
      formData.append('notes', notes)

      await axios.post('/api/stock/add', formData)
      
      toast({
        title: "Success",
        description: "Stock added successfully",
      })
      
      setShowAddForm(false)
      setSelectedProduct('')
      setQuantity('')
      setUnitPrice('')
      setNotes('')
      fetchData()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to add stock",
        variant: "destructive",
      })
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'IN':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'OUT':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'ADJUSTMENT':
        return <Package className="h-4 w-4 text-blue-600" />
      default:
        return <Package className="h-4 w-4 text-gray-600" />
    }
  }

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'IN':
        return 'text-green-600'
      case 'OUT':
        return 'text-red-600'
      case 'ADJUSTMENT':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Stock Management</h1>
          <p className="text-gray-600 mt-2">
            Track stock movements and manage inventory levels.
          </p>
        </div>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Stock
        </Button>
      </div>

      {/* Add Stock Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>Add Stock</CardTitle>
            <CardDescription>
              Add new stock to your inventory
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAddStock} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Product</label>
                  <select
                    value={selectedProduct}
                    onChange={(e) => setSelectedProduct(e.target.value)}
                    className="w-full p-2 border rounded-md"
                    required
                  >
                    <option value="">Select a product</option>
                    {products.map(product => (
                      <option key={product.id} value={product.id}>
                        {product.productCode} - {product.name} (Current: {product.currentStock})
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Quantity</label>
                  <Input
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(e.target.value)}
                    placeholder="Enter quantity to add"
                    min="1"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Unit Price (Optional)</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={unitPrice}
                    onChange={(e) => setUnitPrice(e.target.value)}
                    placeholder="0.00"
                    min="0"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Notes</label>
                  <Input
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Optional notes"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button type="submit">Add Stock</Button>
                <Button type="button" variant="outline" onClick={() => setShowAddForm(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Stock Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Stock Transactions</CardTitle>
          <CardDescription>
            Latest stock movements and adjustments
          </CardDescription>
        </CardHeader>
        <CardContent>
          {transactions.length === 0 ? (
            <div className="text-center py-12">
              <TrendingUp className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                No stock transactions yet
              </h3>
              <p className="mt-2 text-gray-500">
                Start by adding stock to your products.
              </p>
              <Button className="mt-4" onClick={() => setShowAddForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Stock
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {transactions.map(transaction => (
                <div key={transaction.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getTransactionIcon(transaction.type)}
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{transaction.product.productCode}</span>
                          <span className="text-gray-500">-</span>
                          <span>{transaction.product.name}</span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(transaction.createdAt).toLocaleDateString()} at{' '}
                          {new Date(transaction.createdAt).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className={`font-medium ${getTransactionColor(transaction.type)}`}>
                        {transaction.type === 'OUT' ? '-' : '+'}{Math.abs(transaction.quantity)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {transaction.previousStock} → {transaction.newStock}
                      </div>
                    </div>
                  </div>
                  
                  {transaction.notes && (
                    <div className="mt-2 text-sm text-gray-600">
                      <strong>Notes:</strong> {transaction.notes}
                    </div>
                  )}
                  
                  {transaction.totalValue && transaction.totalValue > 0 && (
                    <div className="mt-2 text-sm text-gray-600">
                      <strong>Value:</strong> ${transaction.totalValue.toFixed(2)}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default Stock
