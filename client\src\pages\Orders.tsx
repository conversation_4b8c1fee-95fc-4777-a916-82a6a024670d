import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ShoppingCart, Package, Truck, CheckCircle, Clock, AlertCircle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import axios from 'axios'

interface Order {
  id: string
  orderNumber: string
  clientInfo: {
    name: string
    email: string
  }
  items: Array<{
    productCode: string
    productName: string
    quantity: number
    unitPrice: number
    total: number
    stockDeducted: boolean
  }>
  total: number
  status: 'pending' | 'confirmed' | 'processing' | 'dispatched' | 'delivered' | 'cancelled'
  orderDate: string
  dispatchedDate?: string
  deliveredDate?: string
  trackingNumber?: string
  remarks?: string
  createdAt: string
}

const Orders = () => {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [showStatusUpdate, setShowStatusUpdate] = useState(false)
  const [newStatus, setNewStatus] = useState('')
  const [remarks, setRemarks] = useState('')
  const [trackingNumber, setTrackingNumber] = useState('')
  const { toast } = useToast()

  useEffect(() => {
    fetchOrders()
  }, [])

  const fetchOrders = async () => {
    try {
      const response = await axios.get('/api/orders')
      setOrders(response.data)
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to fetch orders",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'confirmed':
        return <CheckCircle className="h-4 w-4 text-blue-600" />
      case 'processing':
        return <Package className="h-4 w-4 text-purple-600" />
      case 'dispatched':
        return <Truck className="h-4 w-4 text-orange-600" />
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'cancelled':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'confirmed':
        return 'bg-blue-100 text-blue-800'
      case 'processing':
        return 'bg-purple-100 text-purple-800'
      case 'dispatched':
        return 'bg-orange-100 text-orange-800'
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleStatusUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedOrder || !newStatus) return

    try {
      const updateData: any = { status: newStatus }
      if (remarks) updateData.remarks = remarks
      if (trackingNumber) updateData.trackingNumber = trackingNumber
      if (newStatus === 'dispatched') updateData.dispatchedDate = new Date().toISOString()

      await axios.put(`/api/orders/${selectedOrder.id}/status`, updateData)
      
      toast({
        title: "Success",
        description: "Order status updated successfully",
      })
      
      setShowStatusUpdate(false)
      setSelectedOrder(null)
      setNewStatus('')
      setRemarks('')
      setTrackingNumber('')
      fetchOrders()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update order status",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Orders</h1>
          <p className="text-gray-600 mt-2">
            Track and manage your customer orders.
          </p>
        </div>
      </div>

      {/* Orders List */}
      <Card>
        <CardHeader>
          <CardTitle>Order Management</CardTitle>
          <CardDescription>
            View and update order status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {orders.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingCart className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                No orders yet
              </h3>
              <p className="mt-2 text-gray-500">
                Orders will appear here when quotes are approved and converted.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {orders.map(order => (
                <div key={order.id} className="border rounded-lg p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-semibold">{order.orderNumber}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                      </div>
                      <p className="text-gray-600">{order.clientInfo.name} • {order.clientInfo.email}</p>
                      <p className="text-sm text-gray-500">
                        Order Date: {new Date(order.orderDate).toLocaleDateString()}
                      </p>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">
                        ${order.total.toFixed(2)}
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => {
                          setSelectedOrder(order)
                          setNewStatus(order.status)
                          setRemarks(order.remarks || '')
                          setTrackingNumber(order.trackingNumber || '')
                          setShowStatusUpdate(true)
                        }}
                      >
                        Update Status
                      </Button>
                    </div>
                  </div>

                  {/* Order Items */}
                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-3">Order Items</h4>
                    <div className="space-y-2">
                      {order.items.map((item, index) => (
                        <div key={index} className="flex justify-between items-center text-sm">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{item.productCode}</span>
                            <span>-</span>
                            <span>{item.productName}</span>
                            <span className="text-gray-500">x{item.quantity}</span>
                            {item.stockDeducted && (
                              <CheckCircle className="h-4 w-4 text-green-600" title="Stock deducted" />
                            )}
                          </div>
                          <span className="font-medium">${item.total.toFixed(2)}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Additional Info */}
                  {(order.trackingNumber || order.dispatchedDate || order.remarks) && (
                    <div className="border-t pt-4 mt-4">
                      {order.trackingNumber && (
                        <p className="text-sm text-gray-600">
                          <strong>Tracking Number:</strong> {order.trackingNumber}
                        </p>
                      )}
                      {order.dispatchedDate && (
                        <p className="text-sm text-gray-600">
                          <strong>Dispatched:</strong> {new Date(order.dispatchedDate).toLocaleDateString()}
                        </p>
                      )}
                      {order.remarks && (
                        <p className="text-sm text-gray-600">
                          <strong>Remarks:</strong> {order.remarks}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status Update Modal */}
      {showStatusUpdate && selectedOrder && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Update Order Status</CardTitle>
              <CardDescription>
                Update status for order {selectedOrder.orderNumber}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleStatusUpdate} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Status</label>
                  <select
                    value={newStatus}
                    onChange={(e) => setNewStatus(e.target.value)}
                    className="w-full p-2 border rounded-md"
                    required
                  >
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="processing">Processing</option>
                    <option value="dispatched">Dispatched</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Tracking Number</label>
                  <input
                    type="text"
                    value={trackingNumber}
                    onChange={(e) => setTrackingNumber(e.target.value)}
                    className="w-full p-2 border rounded-md"
                    placeholder="Enter tracking number"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Remarks</label>
                  <textarea
                    value={remarks}
                    onChange={(e) => setRemarks(e.target.value)}
                    className="w-full p-2 border rounded-md"
                    rows={3}
                    placeholder="Additional remarks"
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button type="submit">Update Status</Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setShowStatusUpdate(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

export default Orders
