// Simple in-memory storage for development when MongoDB is not available
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

class MemoryStore {
  constructor() {
    this.users = new Map();
    this.invoices = new Map();
    this.quotes = new Map();
    this.products = new Map();
    this.stockTransactions = new Map();
    this.orders = new Map();
    this.userIdCounter = 1;
    this.invoiceIdCounter = 1;
    this.quoteIdCounter = 1;
    this.productIdCounter = 1;
    this.stockTransactionIdCounter = 1;
    this.orderIdCounter = 1;
  }

  // User methods
  async createUser(userData) {
    const { name, email, password } = userData;
    
    // Check if user already exists
    for (const [id, user] of this.users) {
      if (user.email === email.toLowerCase()) {
        throw new Error('User already exists');
      }
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    const user = {
      id: this.userIdCounter++,
      name,
      email: email.toLowerCase(),
      password: hashedPassword,
      createdAt: new Date(),
      isActive: true
    };

    this.users.set(user.id, user);
    return { ...user, password: undefined }; // Don't return password
  }

  async findUserByEmail(email) {
    for (const [id, user] of this.users) {
      if (user.email === email.toLowerCase()) {
        return user;
      }
    }
    return null;
  }

  async findUserById(id) {
    return this.users.get(parseInt(id)) || null;
  }

  async validatePassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  generateToken(userId) {
    return jwt.sign(
      { user: { id: userId } },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );
  }

  // Invoice methods
  createInvoice(invoiceData) {
    const invoice = {
      id: this.invoiceIdCounter++,
      ...invoiceData,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.invoices.set(invoice.id, invoice);
    return invoice;
  }

  getInvoicesByUser(userId) {
    const userInvoices = [];
    for (const [id, invoice] of this.invoices) {
      if (invoice.user === parseInt(userId)) {
        userInvoices.push(invoice);
      }
    }
    return userInvoices.sort((a, b) => b.createdAt - a.createdAt);
  }

  getInvoiceById(id, userId) {
    const invoice = this.invoices.get(parseInt(id));
    if (invoice && invoice.user === parseInt(userId)) {
      return invoice;
    }
    return null;
  }

  // Quote methods
  createQuote(quoteData) {
    const quote = {
      id: this.quoteIdCounter++,
      ...quoteData,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.quotes.set(quote.id, quote);
    return quote;
  }

  getQuotesByUser(userId) {
    const userQuotes = [];
    for (const [id, quote] of this.quotes) {
      if (quote.user === parseInt(userId)) {
        userQuotes.push(quote);
      }
    }
    return userQuotes.sort((a, b) => b.createdAt - a.createdAt);
  }

  getQuoteById(id, userId) {
    const quote = this.quotes.get(parseInt(id));
    if (quote && quote.user === parseInt(userId)) {
      return quote;
    }
    return null;
  }

  // Product methods
  createProduct(productData) {
    const product = {
      id: this.productIdCounter++,
      ...productData,
      currentStock: productData.currentStock || 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };

    if (!product.productCode) {
      product.productCode = `PRD-${String(this.productIdCounter - 1).padStart(6, '0')}`;
    }

    this.products.set(product.id, product);
    return product;
  }

  getProductsByUser(userId, filters = {}) {
    const userProducts = [];
    for (const [id, product] of this.products) {
      if (product.user === parseInt(userId) && product.isActive) {
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          if (!product.productCode.toLowerCase().includes(searchLower) &&
              !product.name.toLowerCase().includes(searchLower) &&
              !(product.description || '').toLowerCase().includes(searchLower)) {
            continue;
          }
        }

        if (filters.category && product.category !== filters.category) {
          continue;
        }

        if (filters.lowStock === 'true' && product.currentStock > (product.minStockLevel || 0)) {
          continue;
        }

        userProducts.push(product);
      }
    }
    return userProducts.sort((a, b) => b.createdAt - a.createdAt);
  }

  getProductById(id, userId) {
    const product = this.products.get(parseInt(id));
    if (product && product.user === parseInt(userId)) {
      return product;
    }
    return null;
  }

  updateProduct(id, updateData, userId) {
    const product = this.products.get(parseInt(id));
    if (product && product.user === parseInt(userId)) {
      Object.assign(product, updateData, { updatedAt: new Date() });
      return product;
    }
    return null;
  }

  deleteProduct(id, userId) {
    const product = this.products.get(parseInt(id));
    if (product && product.user === parseInt(userId)) {
      product.isActive = false;
      return true;
    }
    return false;
  }

  // Stock transaction methods
  addStock({ productId, quantity, unitPrice, notes, documents, userId }) {
    const product = this.getProductById(productId, userId);
    if (!product) {
      return { success: false, message: 'Product not found' };
    }

    const previousStock = product.currentStock;
    const newStock = previousStock + quantity;

    const transaction = {
      id: this.stockTransactionIdCounter++,
      user: parseInt(userId),
      product: parseInt(productId),
      type: 'IN',
      quantity,
      previousStock,
      newStock,
      unitPrice: unitPrice || 0,
      totalValue: quantity * (unitPrice || 0),
      reference: 'MANUAL',
      notes,
      documents: documents || [],
      createdAt: new Date(),
      createdBy: parseInt(userId)
    };

    this.stockTransactions.set(transaction.id, transaction);
    product.currentStock = newStock;
    product.updatedAt = new Date();

    return { success: true, data: transaction };
  }

  adjustStock({ productId, newQuantity, notes, userId }) {
    const product = this.getProductById(productId, userId);
    if (!product) {
      return { success: false, message: 'Product not found' };
    }

    const previousStock = product.currentStock;
    const adjustmentQuantity = newQuantity - previousStock;

    if (adjustmentQuantity === 0) {
      return { success: false, message: 'No adjustment needed' };
    }

    const transaction = {
      id: this.stockTransactionIdCounter++,
      user: parseInt(userId),
      product: parseInt(productId),
      type: 'ADJUSTMENT',
      quantity: adjustmentQuantity,
      previousStock,
      newStock: newQuantity,
      reference: 'ADJUSTMENT',
      notes: notes || 'Manual stock adjustment',
      createdAt: new Date(),
      createdBy: parseInt(userId)
    };

    this.stockTransactions.set(transaction.id, transaction);
    product.currentStock = newQuantity;
    product.updatedAt = new Date();

    return { success: true, data: transaction };
  }

  getStockTransactions(productId, userId) {
    const transactions = [];
    for (const [id, transaction] of this.stockTransactions) {
      if (transaction.user === parseInt(userId) && transaction.product === parseInt(productId)) {
        transactions.push(transaction);
      }
    }
    return transactions.sort((a, b) => b.createdAt - a.createdAt);
  }

  getAllStockTransactions(userId, filters = {}) {
    const transactions = [];
    for (const [id, transaction] of this.stockTransactions) {
      if (transaction.user === parseInt(userId)) {
        if (filters.type && transaction.type !== filters.type) continue;
        if (filters.startDate && transaction.createdAt < new Date(filters.startDate)) continue;
        if (filters.endDate && transaction.createdAt > new Date(filters.endDate)) continue;
        transactions.push(transaction);
      }
    }
    return transactions.sort((a, b) => b.createdAt - a.createdAt).slice(0, parseInt(filters.limit) || 50);
  }

  // Order methods
  createOrderFromQuote(quoteId, userId) {
    const quote = this.getQuoteById(quoteId, userId);
    if (!quote) {
      return { success: false, message: 'Quote not found' };
    }

    if (!quote.isApproved) {
      return { success: false, message: 'Quote must be approved before creating order' };
    }

    if (quote.status === 'converted' || quote.convertedToOrder) {
      return { success: false, message: 'Quote has already been converted to order' };
    }

    // Check stock availability
    for (const item of quote.items) {
      const product = this.getProductById(item.product, userId);
      if (!product) {
        return { success: false, message: `Product ${item.productCode} not found` };
      }

      if (product.currentStock < item.quantity) {
        return {
          success: false,
          message: `Insufficient stock for ${item.productCode}. Available: ${product.currentStock}, Required: ${item.quantity}`
        };
      }
    }

    const order = {
      id: this.orderIdCounter++,
      orderNumber: `ORD-${String(this.orderIdCounter - 1).padStart(6, '0')}`,
      user: parseInt(userId),
      quote: parseInt(quoteId),
      clientInfo: quote.clientInfo,
      items: quote.items.map(item => ({
        ...item,
        stockDeducted: false
      })),
      subtotal: quote.subtotal,
      taxRate: quote.taxRate,
      taxAmount: quote.taxAmount,
      discountRate: quote.discountRate,
      discountAmount: quote.discountAmount,
      total: quote.total,
      status: 'pending',
      orderDate: new Date(),
      notes: quote.notes,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.orders.set(order.id, order);

    // Update quote status
    quote.status = 'converted';
    quote.convertedToOrder = order.id;

    // Deduct stock and create transactions
    for (const item of order.items) {
      const product = this.getProductById(item.product, userId);
      const previousStock = product.currentStock;
      const newStock = previousStock - item.quantity;

      // Create stock transaction
      const stockTransaction = {
        id: this.stockTransactionIdCounter++,
        user: parseInt(userId),
        product: parseInt(item.product),
        type: 'OUT',
        quantity: -item.quantity,
        previousStock,
        newStock,
        reference: 'ORDER',
        referenceId: order.id,
        notes: `Stock deducted for order ${order.orderNumber}`,
        createdAt: new Date(),
        createdBy: parseInt(userId)
      };

      this.stockTransactions.set(stockTransaction.id, stockTransaction);

      // Update product stock
      product.currentStock = newStock;
      product.updatedAt = new Date();

      // Mark item as stock deducted
      item.stockDeducted = true;
      item.stockDeductedAt = new Date();
    }

    return { success: true, data: order };
  }

  getOrdersByUser(userId, filters = {}) {
    const userOrders = [];
    for (const [id, order] of this.orders) {
      if (order.user === parseInt(userId)) {
        if (filters.status && order.status !== filters.status) continue;
        if (filters.startDate && order.orderDate < new Date(filters.startDate)) continue;
        if (filters.endDate && order.orderDate > new Date(filters.endDate)) continue;
        userOrders.push(order);
      }
    }
    return userOrders.sort((a, b) => b.createdAt - a.createdAt);
  }

  getOrderById(id, userId) {
    const order = this.orders.get(parseInt(id));
    if (order && order.user === parseInt(userId)) {
      return order;
    }
    return null;
  }

  updateOrderStatus(id, updateData, userId) {
    const order = this.orders.get(parseInt(id));
    if (order && order.user === parseInt(userId)) {
      Object.assign(order, updateData, { updatedAt: new Date() });

      if (updateData.status === 'dispatched' && updateData.dispatchedDate) {
        order.dispatchedDate = new Date(updateData.dispatchedDate);
      }

      if (updateData.status === 'delivered') {
        order.deliveredDate = new Date();
      }

      return { success: true, data: order };
    }
    return { success: false, message: 'Order not found' };
  }

  // Get stats
  getStats() {
    return {
      users: this.users.size,
      invoices: this.invoices.size,
      quotes: this.quotes.size,
      products: this.products.size,
      stockTransactions: this.stockTransactions.size,
      orders: this.orders.size
    };
  }
}

module.exports = new MemoryStore();
