const express = require('express');
const auth = require('../middleware/auth');
const Quote = require('../models/Quote');

const router = express.Router();

// @route   GET /api/quotes
// @desc    Get all quotes for the user
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const quotes = await Quote.find({ user: req.user.id }).sort({ createdAt: -1 });
    res.json(quotes);
  } catch (error) {
    console.error(error.message);
    res.status(500).send('Server error');
  }
});

// @route   GET /api/quotes/:id
// @desc    Get quote by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const quote = await Quote.findById(req.params.id);
    
    if (!quote) {
      return res.status(404).json({ message: 'Quote not found' });
    }

    // Check if user owns this quote
    if (quote.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    res.json(quote);
  } catch (error) {
    console.error(error.message);
    if (error.kind === 'ObjectId') {
      return res.status(404).json({ message: 'Quote not found' });
    }
    res.status(500).send('Server error');
  }
});

// @route   POST /api/quotes
// @desc    Create a new quote
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const newQuote = new Quote({
      ...req.body,
      user: req.user.id
    });

    const quote = await newQuote.save();
    res.json(quote);
  } catch (error) {
    console.error(error.message);
    res.status(500).send('Server error');
  }
});

// @route   PUT /api/quotes/:id
// @desc    Update a quote
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    let quote = await Quote.findById(req.params.id);

    if (!quote) {
      return res.status(404).json({ message: 'Quote not found' });
    }

    // Check if user owns this quote
    if (quote.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    quote = await Quote.findByIdAndUpdate(
      req.params.id,
      { $set: req.body },
      { new: true }
    );

    res.json(quote);
  } catch (error) {
    console.error(error.message);
    if (error.kind === 'ObjectId') {
      return res.status(404).json({ message: 'Quote not found' });
    }
    res.status(500).send('Server error');
  }
});

// @route   DELETE /api/quotes/:id
// @desc    Delete a quote
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const quote = await Quote.findById(req.params.id);

    if (!quote) {
      return res.status(404).json({ message: 'Quote not found' });
    }

    // Check if user owns this quote
    if (quote.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    await Quote.findByIdAndDelete(req.params.id);
    res.json({ message: 'Quote removed' });
  } catch (error) {
    console.error(error.message);
    if (error.kind === 'ObjectId') {
      return res.status(404).json({ message: 'Quote not found' });
    }
    res.status(500).send('Server error');
  }
});

// @route   POST /api/quotes/:id/convert
// @desc    Convert quote to invoice
// @access  Private
router.post('/:id/convert', auth, async (req, res) => {
  try {
    const quote = await Quote.findById(req.params.id);
    
    if (!quote) {
      return res.status(404).json({ message: 'Quote not found' });
    }

    // Check if user owns this quote
    if (quote.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    // Create invoice from quote
    const Invoice = require('../models/Invoice');
    const invoiceData = {
      ...quote.toObject(),
      _id: undefined,
      status: 'pending',
      type: 'invoice',
      convertedFrom: quote._id
    };

    const invoice = new Invoice(invoiceData);
    await invoice.save();

    // Update quote status
    quote.status = 'converted';
    await quote.save();

    res.json({ quote, invoice });
  } catch (error) {
    console.error(error.message);
    res.status(500).send('Server error');
  }
});

module.exports = router;
