const express = require('express');
const mongoose = require('mongoose');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const auth = require('../middleware/auth');
const Product = require('../models/Product');
const StockTransaction = require('../models/StockTransaction');
const memoryStore = require('../utils/memoryStore');

const router = express.Router();

// Helper function to check if we should use memory store
const useMemoryStore = () => mongoose.connection.readyState !== 1;

// Configure multer for document uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/documents';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /pdf|doc|docx|jpg|jpeg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    
    if (extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only PDF, Word documents, and images are allowed'));
    }
  }
});

// @route   POST /api/stock/add
// @desc    Add stock to a product
// @access  Private
router.post('/add', auth, upload.array('documents', 5), async (req, res) => {
  try {
    const { productId, quantity, unitPrice, notes } = req.body;

    if (useMemoryStore()) {
      const result = memoryStore.addStock({
        productId,
        quantity: parseInt(quantity),
        unitPrice: parseFloat(unitPrice),
        notes,
        documents: req.files || [],
        userId: req.user.id
      });
      
      if (!result.success) {
        return res.status(404).json({ message: result.message });
      }
      
      return res.json(result.data);
    }

    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    if (product.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const previousStock = product.currentStock;
    const newStock = previousStock + parseInt(quantity);

    // Create stock transaction
    const documents = req.files ? req.files.map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      path: file.path
    })) : [];

    const stockTransaction = new StockTransaction({
      user: req.user.id,
      product: productId,
      type: 'IN',
      quantity: parseInt(quantity),
      previousStock,
      newStock,
      unitPrice: parseFloat(unitPrice) || 0,
      reference: 'MANUAL',
      notes,
      documents,
      createdBy: req.user.id
    });

    await stockTransaction.save();

    // Update product stock
    product.currentStock = newStock;
    await product.save();

    // Populate the transaction for response
    await stockTransaction.populate('product', 'productCode name');

    res.json(stockTransaction);
  } catch (error) {
    console.error(error.message);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/stock/transactions/:productId
// @desc    Get stock transactions for a product
// @access  Private
router.get('/transactions/:productId', auth, async (req, res) => {
  try {
    if (useMemoryStore()) {
      const transactions = memoryStore.getStockTransactions(req.params.productId, req.user.id);
      return res.json(transactions);
    }

    const transactions = await StockTransaction.find({
      user: req.user.id,
      product: req.params.productId
    })
    .populate('product', 'productCode name')
    .populate('createdBy', 'name')
    .sort({ createdAt: -1 });

    res.json(transactions);
  } catch (error) {
    console.error(error.message);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/stock/transactions
// @desc    Get all stock transactions for the user
// @access  Private
router.get('/transactions', auth, async (req, res) => {
  try {
    const { type, startDate, endDate, limit = 50 } = req.query;

    if (useMemoryStore()) {
      const transactions = memoryStore.getAllStockTransactions(req.user.id, { type, startDate, endDate, limit });
      return res.json(transactions);
    }

    let query = { user: req.user.id };
    
    if (type) {
      query.type = type;
    }
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    const transactions = await StockTransaction.find(query)
      .populate('product', 'productCode name image')
      .populate('createdBy', 'name')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit));

    res.json(transactions);
  } catch (error) {
    console.error(error.message);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/stock/adjust
// @desc    Adjust stock (manual correction)
// @access  Private
router.post('/adjust', auth, async (req, res) => {
  try {
    const { productId, newQuantity, notes } = req.body;

    if (useMemoryStore()) {
      const result = memoryStore.adjustStock({
        productId,
        newQuantity: parseInt(newQuantity),
        notes,
        userId: req.user.id
      });
      
      if (!result.success) {
        return res.status(404).json({ message: result.message });
      }
      
      return res.json(result.data);
    }

    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    if (product.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const previousStock = product.currentStock;
    const adjustmentQuantity = parseInt(newQuantity) - previousStock;

    if (adjustmentQuantity === 0) {
      return res.status(400).json({ message: 'No adjustment needed' });
    }

    // Create stock transaction
    const stockTransaction = new StockTransaction({
      user: req.user.id,
      product: productId,
      type: 'ADJUSTMENT',
      quantity: adjustmentQuantity,
      previousStock,
      newStock: parseInt(newQuantity),
      reference: 'ADJUSTMENT',
      notes: notes || 'Manual stock adjustment',
      createdBy: req.user.id
    });

    await stockTransaction.save();

    // Update product stock
    product.currentStock = parseInt(newQuantity);
    await product.save();

    await stockTransaction.populate('product', 'productCode name');

    res.json(stockTransaction);
  } catch (error) {
    console.error(error.message);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
