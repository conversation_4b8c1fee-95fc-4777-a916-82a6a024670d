import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText, Quote, DollarSign, TrendingUp, Package, ShoppingCart, AlertTriangle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import axios from 'axios'

interface DashboardStats {
  products: number
  lowStockProducts: number
  totalStock: number
  orders: number
  quotes: number
  invoices: number
}

const Dashboard = () => {
  const { user } = useAuth()
  const { toast } = useToast()
  const [stats, setStats] = useState<DashboardStats>({
    products: 0,
    lowStockProducts: 0,
    totalStock: 0,
    orders: 0,
    quotes: 0,
    invoices: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      const [productsRes, ordersRes, quotesRes, invoicesRes] = await Promise.all([
        axios.get('/api/products'),
        axios.get('/api/orders'),
        axios.get('/api/quotes'),
        axios.get('/api/invoices')
      ])

      const products = productsRes.data
      const lowStockProducts = products.filter((p: any) => p.currentStock <= p.minStockLevel)
      const totalStock = products.reduce((sum: number, p: any) => sum + p.currentStock, 0)

      setStats({
        products: products.length,
        lowStockProducts: lowStockProducts.length,
        totalStock,
        orders: ordersRes.data.length,
        quotes: quotesRes.data.length,
        invoices: invoicesRes.data.length
      })
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to fetch dashboard data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const dashboardCards = [
    {
      title: 'Total Products',
      value: stats.products.toString(),
      description: 'Products in inventory',
      icon: Package,
      color: 'text-blue-600'
    },
    {
      title: 'Low Stock Alert',
      value: stats.lowStockProducts.toString(),
      description: 'Products running low',
      icon: AlertTriangle,
      color: stats.lowStockProducts > 0 ? 'text-red-600' : 'text-green-600'
    },
    {
      title: 'Total Stock',
      value: stats.totalStock.toString(),
      description: 'Items in stock',
      icon: TrendingUp,
      color: 'text-purple-600'
    },
    {
      title: 'Active Orders',
      value: stats.orders.toString(),
      description: 'Customer orders',
      icon: ShoppingCart,
      color: 'text-orange-600'
    },
    {
      title: 'Total Quotes',
      value: stats.quotes.toString(),
      description: 'Pending quotes',
      icon: Quote,
      color: 'text-green-600'
    },
    {
      title: 'Total Invoices',
      value: stats.invoices.toString(),
      description: 'Generated invoices',
      icon: FileText,
      color: 'text-indigo-600'
    }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-gray-600 mt-2">
          Here's an overview of your invoice and quote management system.
        </p>
      </div>

      {/* Stats Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {dashboardCards.map((stat) => {
            const Icon = stat.icon
            return (
              <Card key={stat.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {stat.title}
                  </CardTitle>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground">
                    {stat.description}
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
            <CardDescription>
              Your latest order activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-gray-500">
              No recent orders. Orders will appear when quotes are approved.
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Low Stock Products</CardTitle>
            <CardDescription>
              Products that need restocking
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-gray-500">
              {stats.lowStockProducts === 0
                ? "All products are well stocked!"
                : `${stats.lowStockProducts} products need attention`
              }
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default Dashboard
