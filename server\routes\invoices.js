const express = require('express');
const auth = require('../middleware/auth');
const Invoice = require('../models/Invoice');

const router = express.Router();

// @route   GET /api/invoices
// @desc    Get all invoices for the user
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const invoices = await Invoice.find({ user: req.user.id }).sort({ createdAt: -1 });
    res.json(invoices);
  } catch (error) {
    console.error(error.message);
    res.status(500).send('Server error');
  }
});

// @route   GET /api/invoices/:id
// @desc    Get invoice by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id);
    
    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Check if user owns this invoice
    if (invoice.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    res.json(invoice);
  } catch (error) {
    console.error(error.message);
    if (error.kind === 'ObjectId') {
      return res.status(404).json({ message: 'Invoice not found' });
    }
    res.status(500).send('Server error');
  }
});

// @route   POST /api/invoices
// @desc    Create a new invoice
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const newInvoice = new Invoice({
      ...req.body,
      user: req.user.id
    });

    const invoice = await newInvoice.save();
    res.json(invoice);
  } catch (error) {
    console.error(error.message);
    res.status(500).send('Server error');
  }
});

// @route   PUT /api/invoices/:id
// @desc    Update an invoice
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    let invoice = await Invoice.findById(req.params.id);

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Check if user owns this invoice
    if (invoice.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    invoice = await Invoice.findByIdAndUpdate(
      req.params.id,
      { $set: req.body },
      { new: true }
    );

    res.json(invoice);
  } catch (error) {
    console.error(error.message);
    if (error.kind === 'ObjectId') {
      return res.status(404).json({ message: 'Invoice not found' });
    }
    res.status(500).send('Server error');
  }
});

// @route   DELETE /api/invoices/:id
// @desc    Delete an invoice
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id);

    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Check if user owns this invoice
    if (invoice.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    await Invoice.findByIdAndDelete(req.params.id);
    res.json({ message: 'Invoice removed' });
  } catch (error) {
    console.error(error.message);
    if (error.kind === 'ObjectId') {
      return res.status(404).json({ message: 'Invoice not found' });
    }
    res.status(500).send('Server error');
  }
});

module.exports = router;
