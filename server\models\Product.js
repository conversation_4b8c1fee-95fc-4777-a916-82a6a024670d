const mongoose = require('mongoose');

const ProductSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  productCode: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    trim: true
  },
  image: {
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    path: String
  },
  currentStock: {
    type: Number,
    default: 0,
    min: 0
  },
  unitPrice: {
    type: Number,
    default: 0,
    min: 0
  },
  minStockLevel: {
    type: Number,
    default: 0,
    min: 0
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for faster searches
ProductSchema.index({ user: 1, productCode: 1 });
ProductSchema.index({ user: 1, name: 'text', description: 'text' });

// Virtual for low stock alert
ProductSchema.virtual('isLowStock').get(function() {
  return this.currentStock <= this.minStockLevel;
});

// Generate unique product code if not provided
ProductSchema.pre('save', async function(next) {
  if (!this.productCode) {
    const count = await mongoose.model('Product').countDocuments({ user: this.user });
    this.productCode = `PRD-${String(count + 1).padStart(6, '0')}`;
  }
  next();
});

module.exports = mongoose.model('Product', ProductSchema);
