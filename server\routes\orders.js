const express = require('express');
const mongoose = require('mongoose');
const auth = require('../middleware/auth');
const Order = require('../models/Order');
const Quote = require('../models/Quote');
const Product = require('../models/Product');
const StockTransaction = require('../models/StockTransaction');
const memoryStore = require('../utils/memoryStore');

const router = express.Router();

// Helper function to check if we should use memory store
const useMemoryStore = () => mongoose.connection.readyState !== 1;

// @route   GET /api/orders
// @desc    Get all orders for the user
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { status, startDate, endDate } = req.query;

    if (useMemoryStore()) {
      const orders = memoryStore.getOrdersByUser(req.user.id, { status, startDate, endDate });
      return res.json(orders);
    }

    let query = { user: req.user.id };
    
    if (status) {
      query.status = status;
    }
    
    if (startDate || endDate) {
      query.orderDate = {};
      if (startDate) query.orderDate.$gte = new Date(startDate);
      if (endDate) query.orderDate.$lte = new Date(endDate);
    }

    const orders = await Order.find(query)
      .populate('quote', 'quoteNumber')
      .sort({ createdAt: -1 });

    res.json(orders);
  } catch (error) {
    console.error(error.message);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/orders/:id
// @desc    Get order by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    if (useMemoryStore()) {
      const order = memoryStore.getOrderById(req.params.id, req.user.id);
      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }
      return res.json(order);
    }

    const order = await Order.findById(req.params.id)
      .populate('quote')
      .populate('items.product', 'productCode name image');
    
    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    if (order.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    res.json(order);
  } catch (error) {
    console.error(error.message);
    if (error.kind === 'ObjectId') {
      return res.status(404).json({ message: 'Order not found' });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/orders/from-quote/:quoteId
// @desc    Create order from approved quote
// @access  Private
router.post('/from-quote/:quoteId', auth, async (req, res) => {
  try {
    if (useMemoryStore()) {
      const result = memoryStore.createOrderFromQuote(req.params.quoteId, req.user.id);
      if (!result.success) {
        return res.status(400).json({ message: result.message });
      }
      return res.json(result.data);
    }

    const quote = await Quote.findById(req.params.quoteId).populate('items.product');
    
    if (!quote) {
      return res.status(404).json({ message: 'Quote not found' });
    }

    if (quote.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    if (!quote.isApproved) {
      return res.status(400).json({ message: 'Quote must be approved before creating order' });
    }

    if (quote.status === 'converted' || quote.convertedToOrder) {
      return res.status(400).json({ message: 'Quote has already been converted to order' });
    }

    // Check stock availability
    for (const item of quote.items) {
      const product = await Product.findById(item.product);
      if (!product) {
        return res.status(400).json({ message: `Product ${item.productCode} not found` });
      }
      
      if (product.currentStock < item.quantity) {
        return res.status(400).json({ 
          message: `Insufficient stock for ${item.productCode}. Available: ${product.currentStock}, Required: ${item.quantity}` 
        });
      }
    }

    // Create order
    const orderData = {
      user: req.user.id,
      quote: quote._id,
      clientInfo: quote.clientInfo,
      items: quote.items.map(item => ({
        product: item.product._id,
        productCode: item.productCode,
        productName: item.productName,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.total,
        stockDeducted: false
      })),
      subtotal: quote.subtotal,
      taxRate: quote.taxRate,
      taxAmount: quote.taxAmount,
      discountRate: quote.discountRate,
      discountAmount: quote.discountAmount,
      total: quote.total,
      notes: quote.notes
    };

    const order = new Order(orderData);
    await order.save();

    // Update quote status
    quote.status = 'converted';
    quote.convertedToOrder = order._id;
    await quote.save();

    // Deduct stock and create transactions
    for (const item of order.items) {
      const product = await Product.findById(item.product);
      const previousStock = product.currentStock;
      const newStock = previousStock - item.quantity;

      // Create stock transaction
      const stockTransaction = new StockTransaction({
        user: req.user.id,
        product: item.product,
        type: 'OUT',
        quantity: -item.quantity, // Negative for outgoing
        previousStock,
        newStock,
        reference: 'ORDER',
        referenceId: order._id,
        referenceModel: 'Order',
        notes: `Stock deducted for order ${order.orderNumber}`,
        createdBy: req.user.id
      });

      await stockTransaction.save();

      // Update product stock
      product.currentStock = newStock;
      await product.save();

      // Mark item as stock deducted
      const orderItem = order.items.find(oi => oi.product.toString() === item.product.toString());
      if (orderItem) {
        orderItem.stockDeducted = true;
        orderItem.stockDeductedAt = new Date();
      }
    }

    await order.save();

    res.json(order);
  } catch (error) {
    console.error(error.message);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/orders/:id/status
// @desc    Update order status
// @access  Private
router.put('/:id/status', auth, async (req, res) => {
  try {
    const { status, remarks, dispatchedDate, trackingNumber } = req.body;

    if (useMemoryStore()) {
      const result = memoryStore.updateOrderStatus(req.params.id, { status, remarks, dispatchedDate, trackingNumber }, req.user.id);
      if (!result.success) {
        return res.status(404).json({ message: result.message });
      }
      return res.json(result.data);
    }

    let order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    if (order.user.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    const updateData = { status };
    
    if (remarks) updateData.remarks = remarks;
    if (trackingNumber) updateData.trackingNumber = trackingNumber;
    
    if (status === 'dispatched' && dispatchedDate) {
      updateData.dispatchedDate = new Date(dispatchedDate);
    }
    
    if (status === 'delivered') {
      updateData.deliveredDate = new Date();
    }

    order = await Order.findByIdAndUpdate(
      req.params.id,
      { $set: updateData },
      { new: true }
    );

    res.json(order);
  } catch (error) {
    console.error(error.message);
    if (error.kind === 'ObjectId') {
      return res.status(404).json({ message: 'Order not found' });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
