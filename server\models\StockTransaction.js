const mongoose = require('mongoose');

const StockTransactionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  type: {
    type: String,
    enum: ['IN', 'OUT', 'ADJUSTMENT'],
    required: true
  },
  quantity: {
    type: Number,
    required: true
  },
  previousStock: {
    type: Number,
    required: true
  },
  newStock: {
    type: Number,
    required: true
  },
  unitPrice: {
    type: Number,
    default: 0
  },
  totalValue: {
    type: Number,
    default: 0
  },
  reference: {
    type: String,
    enum: ['PURCHASE', 'SALE', 'QUOTATION', 'ORDER', 'ADJUSTMENT', 'MANUAL'],
    required: true
  },
  referenceId: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'referenceModel'
  },
  referenceModel: {
    type: String,
    enum: ['Quote', 'Order', 'StockAdjustment']
  },
  notes: {
    type: String,
    trim: true
  },
  documents: [{
    filename: String,
    originalName: String,
    mimetype: String,
    size: Number,
    path: String
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Index for faster queries
StockTransactionSchema.index({ user: 1, product: 1, createdAt: -1 });
StockTransactionSchema.index({ user: 1, type: 1, createdAt: -1 });

// Calculate total value before saving
StockTransactionSchema.pre('save', function(next) {
  if (this.type === 'IN') {
    this.totalValue = Math.abs(this.quantity) * this.unitPrice;
  } else {
    this.totalValue = 0; // For OUT transactions, we don't track value
  }
  next();
});

module.exports = mongoose.model('StockTransaction', StockTransactionSchema);
